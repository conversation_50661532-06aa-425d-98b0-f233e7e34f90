const express = require('express');
const axios = require('axios');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');
const { HttpsProxyAgent } = require('https-proxy-agent');

const app = express();
const PORT = process.env.PORT || 3000;

// Cookie存储文件路径
const COOKIE_FILE = path.join(__dirname, 'cookie.json');

// 代理API URL
const PROXY_API_URL = '';

// 代理数组，方便用户填入代理 - 通用IP轮询
const PROXIES = [
  {
    host: '***************',
    port: 1080,
    username: 'vip',
    password: 'c94532twkh',
    protocol: 'socks5'
  },
  {
    host: '***************',
    port: 1080,
    username: 'vip',
    password: 'c94532twkh',
    protocol: 'socks5'
  }
];

// 代理轮询索引
let currentProxyIndex = 0;

// 初始化Cookie
let currentCookie = loadCookie();
let seller_id = extractSellerIdFromCookie(currentCookie) || '2749342'; // Fallback to default if not found

// Extract seller_id from cookie
function extractSellerIdFromCookie(cookie) {
  if (!cookie) return null;
  
  const cookieParts = cookie.split(';');
  for (const part of cookieParts) {
    const [key, value] = part.trim().split('=');
    if (key === 'sc_company_id') {
      return value;
    }
  }
  return null;
}

// 加载Cookie
function loadCookie() {
  try {
    if (fs.existsSync(COOKIE_FILE)) {
      const data = fs.readFileSync(COOKIE_FILE, 'utf8');
      return JSON.parse(data).cookie;
    }
  } catch (err) {
    console.error('Error loading cookie:', err);
  }
  return null;
}

// 保存Cookie
function saveCookie(cookie) {
  try {
    fs.writeFileSync(COOKIE_FILE, JSON.stringify({ cookie }, null, 2));
    currentCookie = cookie;
    console.log('Cookie updated successfully');
  } catch (err) {
    console.error('Error saving cookie:', err);
  }
}

// 获取代理信息的函数 - 通用IP轮询
async function getProxy() {
  try {
    // 如果有外部代理API，优先使用
    if (PROXY_API_URL) {
      try {
        const response = await axios.get(PROXY_API_URL);
        const proxyData = response.data.trim();

        // 检查返回的数据是否为空
        if (proxyData) {
          // 解析代理信息 (格式: IP:端口)
          const [host, port] = proxyData.split(':');

          // 检查解析结果
          if (host && port) {
            console.log(`Using proxy from API: ${host}:${port}`);
            return { host, port: parseInt(port, 10) };
          }
        }
        console.warn('Proxy API returned invalid data, falling back to configured proxies');
      } catch (apiError) {
        console.warn('Failed to fetch proxy from API:', apiError.message, 'falling back to configured proxies');
      }
    }

    // 使用配置的代理进行轮询
    if (PROXIES.length === 0) {
      console.warn('No proxies configured');
      return null;
    }

    // 轮询选择代理
    const proxy = PROXIES[currentProxyIndex];
    currentProxyIndex = (currentProxyIndex + 1) % PROXIES.length;

    console.log(`Using proxy (${currentProxyIndex}/${PROXIES.length}): ${proxy.host}:${proxy.port}`);
    return proxy;

  } catch (error) {
    console.error('Error in getProxy:', error.message);

    // 出错时也尝试轮询返回一个代理
    if (PROXIES.length > 0) {
      const proxy = PROXIES[currentProxyIndex];
      currentProxyIndex = (currentProxyIndex + 1) % PROXIES.length;
      console.log(`Fallback to proxy: ${proxy.host}:${proxy.port}`);
      return proxy;
    }

    return null;
  }
}

// 使用body-parser中间件解析请求体
app.use(bodyParser.json());

// 设置CORS头，允许所有来源访问资源
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    next();
});

// 合并Cookie的函数
const mergeCookies = (existingCookie, newCookies) => {
  const cookieDict = {};
  
  // 解析现有Cookie
  if (existingCookie) {
    existingCookie.split(';').forEach(pair => {
      const [key, ...values] = pair.trim().split('=');
      if (key) cookieDict[key] = values.join('=');
    });
  }

  // 解析新Cookie
  newCookies.forEach(cookie => {
    const [keyValue] = cookie.split(';');
    const [key, ...values] = keyValue.trim().split('=');
    if (key) cookieDict[key] = values.join('=');
  });

  // 生成新Cookie字符串
  return Object.entries(cookieDict)
    .map(([key, value]) => `${key}=${value}`)
    .join('; ');
};

// 带Cookie更新功能的请求函数
async function makeRequest(config) {
  try {
    const response = await axios({
      ...config,
      headers: {
        ...config.headers,
        cookie: currentCookie
      }
    });

    // 检查是否有新的Cookie返回
    if (response.headers['set-cookie']) {
      const newCookie = mergeCookies(currentCookie, response.headers['set-cookie']);
      saveCookie(newCookie);
    }

    return response;
  } catch (error) {
    // 处理307重定向和Cookie更新
    if (error.response?.status === 307 && error.response.headers['set-cookie']) {
      const newCookie = mergeCookies(currentCookie, error.response.headers['set-cookie']);
      saveCookie(newCookie);
      
      // 使用新Cookie重试请求
      return makeRequest({
        ...config,
        headers: {
          ...config.headers,
          cookie: newCookie
        }
      });
    }
    throw error;
  }
}

// 更新Cookie的路由
app.post('/api/update-cookie', (req, res) => {
  const { cookie } = req.body;
  if (!cookie) {
    return res.status(400).json({ error: 'Cookie is required' });
  }
  
  saveCookie(cookie);
  res.json({ message: 'Cookie updated successfully' });
});

// 获取当前Cookie的路由
app.get('/api/get-cookie', (req, res) => {
  res.json({ cookie: currentCookie });
});

// 更新seller_id的路由
app.post('/api/update-seller', (req, res) => {
  const { sellerId } = req.body;
  if (!sellerId) {
    return res.status(400).json({ error: 'Seller ID is required' });
  }
  
  seller_id = sellerId;
  res.json({ message: 'Seller ID updated successfully', seller_id });
});

// 修改原有的API路由，使用makeRequestWithRetry
app.post('/api/sku-query', async (req, res) => {
    const { sku, limit = 10, language = "zh-Hans" } = req.body;

    if (!sku) {
        return res.status(400).json({ error: 'SKU is required' });
    }

    try {
        const config = {
            method: 'post',
            url: 'https://seller.ozon.ru/api/v1/search-variant-model',
            data: {
                name: sku,
                limit: limit
            },
            headers: {
                'authority': 'seller.ozon.ru',
                'accept-language': language,
                'origin': 'https://seller.ozon.ru',
                'referer': 'https://seller.ozon.ru/app/products/create',
                'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'x-o3-app-name': 'seller-ui',
                'x-o3-company-id': seller_id,
                'x-o3-language': language,
                'x-o3-page-type': 'products-other'
            }
        };
        const response = await makeRequestWithRetry(config, 15, true);

        res.json(response.data);
    } catch (error) {
        console.error('Error fetching data from OZON API:', error);
        res.status(500).json({ error: 'Failed to fetch data from OZON API' });
    }
});

app.post('/api/sku', async (req, res) => {
    const { sku, limit = 10, offset = 0, period = "monthly", categories = null, company_ids = null, useProxy = true } = req.body;
    var key = "sum_gmv_desc";
    if(sku){
        key = "sum_missed_gmv_desc";
    }

    // 构建请求配置
    const config = {
        method: 'post',
        url: 'https://seller.ozon.ru/api/site/seller-analytics/what_to_sell/data/v3',
        data: {
            'limit': limit,
            'offset': offset,
            'filter': {
                'stock': 'any_stock',
                'period': period,
                'sku': sku,
                'categories': categories,
                'company_ids': company_ids
            },
            'sort': {
                'key': key
            }
        },
        headers: {
            'authority': 'seller.ozon.ru',
            'accept-language': 'zh-Hans',
            'origin': 'https://seller.ozon.ru',
            'referer': 'https://seller.ozon.ru/app/analytics/what-to-sell/ozon-bestsellers',
            'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-o3-app-name': 'seller-ui',
            'x-o3-company-id': seller_id,
            'x-o3-language': 'zh-Hans',
            'x-o3-page-type': 'what-to-sell'
        }
    };

    try {
        const response = await makeRequestWithRetry(config, 15, useProxy);
        res.json(response.data);
    } catch (error) {
        console.error('Error fetching data from OZON API:', error);
        res.status(500).json({ error: 'Failed to fetch data from OZON API', details: error.message });
    }
});

// 带重试机制的请求函数 - 使用通用IP轮询
async function makeRequestWithRetry(config, maxRetries = 15, useProxy = true) { // 增加重试次数和代理选项
  let lastError;
  let proxyErrorCount = 0; // 记录连续代理错误的次数
  const MAX_PROXY_ERRORS = 3; // 最大连续代理错误次数
  let consecutiveProxyFailures = 0; // 连续代理失败次数
  const MAX_CONSECUTIVE_PROXY_FAILURES = 3; // 最大连续代理失败次数
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      let proxy = null;
      // 如果需要使用代理，则获取代理信息
      if (useProxy) {
        proxy = await getProxy();
        
        // 必须使用代理，如果没有获取到代理则等待后重试
        if (!proxy) {
          console.warn(`Attempt ${attempt}: No proxy available, waiting 5 seconds before retry...`);
          if (attempt < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 5000));
          }
          continue;
        }
      } else {
        console.log(`Attempt ${attempt}: Using direct connection (no proxy)`);
      }
      
      // 设置请求配置
      let requestConfig = { ...config };
      
      // 禁用重定向，自己处理
      requestConfig.maxRedirects = 0;
      
      // 添加随机延迟，模拟人类行为，避免被识别为爬虫
      if (attempt > 1) {
        // 增加延迟范围
        const randomDelay = Math.floor(Math.random() * 5000) + 3000; // 3-8秒的随机延迟
        console.log(`Adding random delay of ${randomDelay}ms to avoid being detected as a crawler`);
        await new Promise(resolve => setTimeout(resolve, randomDelay));
      }
      
      // 如果使用代理，则设置代理
      if (useProxy && proxy) {
        // 检查是否是SOCKS5代理
        if (proxy.protocol === 'socks5') {
          // 需要安装socks-proxy-agent包来支持SOCKS5代理
          try {
            const { SocksProxyAgent } = require('socks-proxy-agent');
            let proxyUrl = `socks5://${proxy.host}:${proxy.port}`;
            if (proxy.username && proxy.password) {
              proxyUrl = `socks5://${proxy.username}:${proxy.password}@${proxy.host}:${proxy.port}`;
            }
            const agent = new SocksProxyAgent(proxyUrl);
            requestConfig.httpsAgent = agent;
            requestConfig.httpAgent = agent;
            console.log(`Attempt ${attempt}: Using SOCKS5 proxy: ${proxyUrl}`);
          } catch (error) {
            console.error('Error creating SOCKS5 proxy agent:', error.message);
            // 回退到HTTP代理
            const { HttpsProxyAgent } = require('https-proxy-agent');
            const agent = new HttpsProxyAgent(`http://${proxy.host}:${proxy.port}`, {
              timeout: 10000
            });
            requestConfig.httpsAgent = agent;
            console.log(`Attempt ${attempt}: Using HTTP proxy with HttpsProxyAgent: ${proxy.host}:${proxy.port}`);
          }
        } else {
          // 使用 HttpsProxyAgent 处理 HTTPS 代理
          const { HttpsProxyAgent } = require('https-proxy-agent');
          const agent = new HttpsProxyAgent(`http://${proxy.host}:${proxy.port}`, {
            // 增加超时时间
            timeout: 10000
          });
          requestConfig.httpsAgent = agent;
          console.log(`Attempt ${attempt}: Using HTTP proxy with HttpsProxyAgent: ${proxy.host}:${proxy.port}`);
        }
      } else {
        console.log(`Attempt ${attempt}: Making direct request without proxy`);
        // 确保清除可能存在的代理设置
        delete requestConfig.httpsAgent;
        delete requestConfig.httpAgent;
        delete requestConfig.proxy;
      }
      
      // 发送请求
      const response = await makeRequest(requestConfig);
      
      // 请求成功，重置代理错误计数
      proxyErrorCount = 0;
      consecutiveProxyFailures = 0;
      return response; // 成功则返回响应
    } catch (error) {
      lastError = error;
      console.error(`Attempt ${attempt} failed:`, error.message);
      
      // 检查是否是代理相关的错误
      const isProxyError = error.message.includes('wrong version number') ||
          error.message.includes('SSL') ||
          error.message.includes('socket hang up') ||
          error.message.includes('Proxy connection ended') ||
          error.message.includes('Maximum number of redirects exceeded') ||
          error.message.includes('timeout') || // 超时错误
          (error.response?.status >= 500 && error.response?.status !== 503) || // 503是服务不可用，可能是代理问题
          error.response?.status === 618; // 特定的代理错误状态码
          
      if (isProxyError) {
        proxyErrorCount++;
        consecutiveProxyFailures++;
        console.log(`Proxy error detected (${proxyErrorCount}/${MAX_PROXY_ERRORS}), consecutive failures: ${consecutiveProxyFailures}/${MAX_CONSECUTIVE_PROXY_FAILURES}`);
        
        // 如果是代理连接问题，立即获取新代理
        if (error.message.includes('Proxy connection ended') ||
            error.message.includes('timeout') ||
            error.message.includes('wrong version number')) {
          console.log(`Immediate proxy error, will get new proxy for next attempt`);
          // 添加一个小延迟后继续
          if (attempt < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      } else {
        // 非代理错误，重置计数
        proxyErrorCount = 0;
        consecutiveProxyFailures = 0;
      }
      
      // 处理307重定向和Cookie更新
      if (error.response?.status === 307 && error.response.headers['set-cookie']) {
        const newCookie = mergeCookies(currentCookie, error.response.headers['set-cookie']);
        saveCookie(newCookie);
        console.log(`Attempt ${attempt}: Cookie updated due to 307 redirect`);
        
        // 使用新Cookie重试请求，不需要等待和更换代理
        try {
          let redirectConfig = {
            ...config,
            maxRedirects: 0, // 同样禁用重定向
            headers: {
                ...config.headers,
                cookie: newCookie
            }
          };
          
          // 如果使用代理，则获取新代理
          if (useProxy) {
            const newProxy = await getProxy();
            if (!newProxy) {
              throw new Error('Failed to get new proxy');
            }
            
            // 检查是否是SOCKS5代理
            if (newProxy.protocol === 'socks5') {
              // 需要安装socks-proxy-agent包来支持SOCKS5代理
              try {
                const { SocksProxyAgent } = require('socks-proxy-agent');
                let proxyUrl = `socks5://${newProxy.host}:${newProxy.port}`;
                if (newProxy.username && newProxy.password) {
                  proxyUrl = `socks5://${newProxy.username}:${newProxy.password}@${newProxy.host}:${newProxy.port}`;
                }
                const agent = new SocksProxyAgent(proxyUrl);
                redirectConfig.httpsAgent = agent;
                redirectConfig.httpAgent = agent;
                console.log(`Redirect request: Using SOCKS5 proxy: ${proxyUrl}`);
              } catch (error) {
                console.error('Error creating SOCKS5 proxy agent for redirect:', error.message);
                // 回退到HTTP代理
                const { HttpsProxyAgent } = require('https-proxy-agent');
                const agent = new HttpsProxyAgent(`http://${newProxy.host}:${newProxy.port}`, {
                  timeout: 10000
                });
                redirectConfig.httpsAgent = agent;
                console.log(`Redirect request: Using HTTP proxy with HttpsProxyAgent: ${newProxy.host}:${newProxy.port}`);
              }
            } else {
              // 使用 HttpsProxyAgent 处理 HTTPS 代理
              const { HttpsProxyAgent } = require('https-proxy-agent');
              const agent = new HttpsProxyAgent(`http://${newProxy.host}:${newProxy.port}`, {
                timeout: 10000
              });
              redirectConfig.httpsAgent = agent;
              console.log(`Redirect request: Using HTTP proxy with HttpsProxyAgent: ${newProxy.host}:${newProxy.port}`);
            }
          } else {
            console.log('Redirect request: Making direct request without proxy');
            // 确保清除可能存在的代理设置
            delete redirectConfig.httpsAgent;
            delete redirectConfig.httpAgent;
            delete redirectConfig.proxy;
          }
          
          const response = await makeRequest(redirectConfig);
          
          // 请求成功，重置代理错误计数
          proxyErrorCount = 0;
          consecutiveProxyFailures = 0;
          return response; // 成功则返回响应
        } catch (redirectError) {
          lastError = redirectError;
          console.error(`Attempt ${attempt}: Failed even after cookie update:`, redirectError.message);
        }
      }
      
      // 如果不是最后一次尝试，则等待5秒后重试
      if (attempt < maxRetries) {
        console.log(`Waiting 5 seconds before retry ${attempt + 1}...`);
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  }
  
  // 所有尝试都失败了，抛出最后一个错误
  throw new Error(`Failed after ${maxRetries} attempts. Last error: ${lastError.message}`);
}

app.post('/api/v3', async (req, res) => {
    const { url } = req.body;

    if (!url) {
        return res.status(400).json({ error: 'url is required' });
    }
    
    try {
        const apiurl = 'https://www.ozon.ru/api/entrypoint-api.bx/page/json/v2?url='+url;
        console.log(apiurl);
        
        const response = await makeRequest({
            method: 'get',
            url: apiurl,
            headers: {
                'authority': 'www.ozon.ru',
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'accept-language': 'zh-CN,zh;q=0.9',
                'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'none',
                'sec-fetch-user': '?1',
                'service-worker-navigation-preload': 'true',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
        });
        
        res.json(response.data);
    } catch (error) {
        console.error('Error fetching data from OZON API:', error);
        res.status(500).json({ error: 'Failed to fetch data from OZON API' });
    }
});

app.post('/api/v2', async (req, res) => {
    const { url, proxyHost, proxyPort } = req.body;

    if (!url) {
        return res.status(400).json({ error: 'url is required' });
    }

    try {
        const apiurl = 'https://www.ozon.ru/api/entrypoint-api.bx/page/json/v2?url=' + url;
        
        const config = {
            method: 'get',
            url: apiurl,
            headers: {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'zh-CN,zh;q=0.9',
                'cache-control': 'no-cache',
                'pragma': 'no-cache',
                'priority': 'u=0, i',
                'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'cross-site',
                'sec-fetch-user': '?1',
                'service-worker-navigation-preload': 'true',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
        };

        // 添加代理配置（如果存在）
        if (proxyHost && proxyPort) {
            config.proxy = {
                host: proxyHost,
                port: proxyPort
            };
            console.log(`Using proxy: ${proxyHost}:${proxyPort}`);
        }

        const response = await makeRequest(config);
        res.json(response.data);
    } catch (error) {
        console.error('Error fetching data:', error);
        const statusCode = error.response?.status || 500;
        const errorMessage = error.response?.data?.message || 'Failed to fetch data';
        res.status(statusCode).json({ 
            error: errorMessage,
            details: error.message
        });
    }
});

// 监听指定端口
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`Current cookie: ${currentCookie.substring(0, 50)}...`); // 只显示前50个字符
});